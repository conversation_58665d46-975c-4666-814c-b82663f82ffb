import streamlit as st
import os
from config import THEME_PRIMARY_COLOR, THEME_SECONDARY_COLOR, THEME_BACKGROUND_COLOR, THEME_SECONDARY_BACKGROUND_COLOR, THEME_TEXT_COLOR, THEME_CARD_COLOR, THEME_BORDER_COLOR

# Configure the page
st.set_page_config(
    page_title="Settings - Chatori Padhai",
    page_icon="⚙️",
    layout="wide"
)

# Load custom CSS theme
def load_css():
    with open("theme.css", "r") as f:
        css = f.read()
    st.markdown(f"<style>{css}</style>", unsafe_allow_html=True)

# Apply custom theme
load_css()

# Navigation header
st.markdown("""
<div style="background: linear-gradient(90deg, #ffa31a 0%, #e5921a 100%); padding: 1rem; margin: -1rem -1rem 2rem -1rem; border-radius: 0 0 15px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h1 style="color: white; margin: 0; font-size: 1.8rem; font-weight: 700;">⚙️ Chatori Padhai</h1>
        <div style="display: flex; gap: 1rem;">
            <a href="/" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">🏠 Home</a>
            <a href="/Tutorial" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">🎓 Tutorial</a>
            <a href="/History" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">📚 History</a>
            <a href="/Settings" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.4); border-radius: 8px;">⚙️ Settings</a>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

def main():
    """Main settings page."""
    
    # Page header
    st.markdown(f"""
    <div class="card" style="text-align: center; background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR};">
        <h2 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 0.5rem 0;">⚙️ Application Settings</h2>
        <p style="color: {THEME_TEXT_COLOR}; margin: 0;">Customize your Chatori Padhai experience</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Settings tabs
    tab1, tab2, tab3, tab4 = st.tabs(["🎨 Appearance", "🤖 AI Settings", "📊 Data & Privacy", "ℹ️ About"])
    
    with tab1:
        st.markdown(f"""
        <div class="card" style="background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin-bottom: 1rem;">🎨 Theme & Appearance</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Theme preview
        st.markdown("### Current Theme: Orange/Black Professional")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"""
            <div style="background: {THEME_PRIMARY_COLOR}; padding: 1rem; border-radius: 8px; margin: 0.5rem 0;">
                <h4 style="color: white; margin: 0;">Primary Orange</h4>
                <p style="color: white; margin: 0; font-family: monospace;">{THEME_PRIMARY_COLOR}</p>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown(f"""
            <div style="background: {THEME_SECONDARY_COLOR}; padding: 1rem; border-radius: 8px; margin: 0.5rem 0;">
                <h4 style="color: white; margin: 0;">Secondary Gray</h4>
                <p style="color: white; margin: 0; font-family: monospace;">{THEME_SECONDARY_COLOR}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div style="background: {THEME_BACKGROUND_COLOR}; padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid {THEME_BORDER_COLOR};">
                <h4 style="color: {THEME_TEXT_COLOR}; margin: 0;">Background Black</h4>
                <p style="color: {THEME_SECONDARY_COLOR}; margin: 0; font-family: monospace;">{THEME_BACKGROUND_COLOR}</p>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown(f"""
            <div style="background: {THEME_CARD_COLOR}; padding: 1rem; border-radius: 8px; margin: 0.5rem 0; border: 1px solid {THEME_BORDER_COLOR};">
                <h4 style="color: {THEME_TEXT_COLOR}; margin: 0;">Card Dark Gray</h4>
                <p style="color: {THEME_SECONDARY_COLOR}; margin: 0; font-family: monospace;">{THEME_CARD_COLOR}</p>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown("---")
        
        # Display preferences
        st.markdown("### 📱 Display Preferences")
        
        col1, col2 = st.columns(2)
        
        with col1:
            animation_speed = st.selectbox(
                "Animation Speed",
                ["Slow", "Normal", "Fast", "Disabled"],
                index=1
            )
            
            font_size = st.selectbox(
                "Font Size",
                ["Small", "Normal", "Large", "Extra Large"],
                index=1
            )
        
        with col2:
            compact_mode = st.checkbox("Compact Mode", value=False)
            high_contrast = st.checkbox("High Contrast Mode", value=False)
        
        if st.button("💾 Save Appearance Settings", type="primary"):
            st.success("Appearance settings saved! (Note: Some changes require page refresh)")
    
    with tab2:
        st.markdown(f"""
        <div class="card" style="background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin-bottom: 1rem;">🤖 AI Tutor Configuration</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # AI Model settings
        st.markdown("### 🧠 Model Settings")
        
        col1, col2 = st.columns(2)
        
        with col1:
            tutorial_length = st.selectbox(
                "Tutorial Length",
                ["Short (200-300 words)", "Medium (300-500 words)", "Long (500-800 words)"],
                index=1
            )
            
            difficulty_level = st.selectbox(
                "Default Difficulty Level",
                ["Beginner", "Intermediate", "Advanced", "Mixed"],
                index=0
            )
        
        with col2:
            response_style = st.selectbox(
                "Response Style",
                ["Conversational", "Academic", "Casual", "Professional"],
                index=0
            )
            
            include_examples = st.checkbox("Always Include Examples", value=True)
        
        st.markdown("---")
        
        # Evaluation settings
        st.markdown("### 📝 Evaluation Settings")
        
        col1, col2 = st.columns(2)
        
        with col1:
            max_evaluations = st.number_input(
                "Max Evaluations per Session",
                min_value=1,
                max_value=20,
                value=10
            )
        
        with col2:
            evaluation_difficulty = st.selectbox(
                "Evaluation Difficulty",
                ["Easy", "Medium", "Hard", "Adaptive"],
                index=1
            )
        
        auto_evaluate = st.checkbox("Auto-suggest evaluations", value=True)
        
        if st.button("💾 Save AI Settings", type="primary"):
            st.success("AI settings saved!")
    
    with tab3:
        st.markdown(f"""
        <div class="card" style="background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin-bottom: 1rem;">📊 Data Management & Privacy</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Data storage info
        st.markdown("### 💾 Data Storage")
        
        st.info("""
        **Local Storage:** All your conversations and learning data are stored locally on your device in an SQLite database.
        
        **Privacy:** No personal data is sent to external servers except for AI model API calls (which only include your questions and tutorial content).
        """)
        
        # Database management
        st.markdown("### 🗄️ Database Management")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📊 View Database Stats", use_container_width=True):
                try:
                    from database import TutorialDatabase
                    import sqlite3
                    
                    db = TutorialDatabase()
                    conn = sqlite3.connect(db.db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute("SELECT COUNT(*) FROM conversations")
                    conv_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM messages")
                    msg_count = cursor.fetchone()[0]
                    
                    conn.close()
                    
                    st.success(f"📈 Database Stats:\n- Conversations: {conv_count}\n- Messages: {msg_count}")
                except Exception as e:
                    st.error(f"Error reading database: {str(e)}")
        
        with col2:
            if st.button("📤 Export Data", use_container_width=True):
                st.info("Export functionality coming soon! Your data will be exportable as JSON.")
        
        with col3:
            if st.button("🗑️ Clear All Data", use_container_width=True, type="secondary"):
                st.warning("⚠️ This will delete ALL your learning history permanently!")
                if st.button("⚠️ Confirm Delete All", type="secondary"):
                    try:
                        from database import TutorialDatabase
                        import sqlite3
                        
                        db = TutorialDatabase()
                        conn = sqlite3.connect(db.db_path)
                        cursor = conn.cursor()
                        
                        cursor.execute("DELETE FROM messages")
                        cursor.execute("DELETE FROM conversations")
                        
                        conn.commit()
                        conn.close()
                        
                        st.success("All data cleared successfully!")
                    except Exception as e:
                        st.error(f"Error clearing data: {str(e)}")
        
        st.markdown("---")
        
        # Privacy settings
        st.markdown("### 🔒 Privacy Settings")
        
        analytics_enabled = st.checkbox("Enable usage analytics (local only)", value=False)
        crash_reports = st.checkbox("Send crash reports", value=False)
        
        if st.button("💾 Save Privacy Settings", type="primary"):
            st.success("Privacy settings saved!")
    
    with tab4:
        st.markdown(f"""
        <div class="card" style="background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin-bottom: 1rem;">ℹ️ About Chatori Padhai</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # App info
        st.markdown(f"""
        <div class="card" style="background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 1px solid {THEME_PRIMARY_COLOR};">
            <div style="text-align: center;">
                <h2 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0;">🎓 Chatori Padhai</h2>
                <p style="color: {THEME_TEXT_COLOR}; font-size: 1.1rem; margin: 0 0 1rem 0;">
                    AI-Powered Interactive Learning Platform
                </p>
                <p style="color: {THEME_SECONDARY_COLOR}; margin: 0;">
                    Version 2.0.0 - Modern UI Edition
                </p>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Features
        st.markdown("### ✨ Features")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"""
            - 🎓 **Interactive Tutorials** - AI-generated learning content
            - 💬 **Q&A Sessions** - Ask follow-up questions
            - 🧠 **Knowledge Testing** - Evaluate your understanding
            - 📚 **Learning History** - Track your progress
            """)
        
        with col2:
            st.markdown(f"""
            - 🎨 **Modern UI** - Professional orange/black theme
            - 🔗 **URL Routing** - Proper navigation between pages
            - 💾 **Local Storage** - Your data stays private
            - ⚡ **Fast & Responsive** - Optimized performance
            """)
        
        st.markdown("---")
        
        # Technical info
        st.markdown("### 🛠️ Technical Information")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            **Frontend:** Streamlit with Custom CSS  
            **Backend:** Python with LangGraph  
            **Database:** SQLite  
            **AI Model:** Gemini API  
            """)
        
        with col2:
            st.markdown("""
            **Theme:** Orange/Black Professional  
            **Routing:** Multi-page Streamlit  
            **Deployment:** Streamlit Cloud Ready  
            **License:** MIT License  
            """)
        
        st.markdown("---")
        
        # Support and links
        st.markdown("### 🤝 Support & Resources")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown(f"""
            <div class="card" style="text-align: center; background: {THEME_CARD_COLOR};">
                <h4 style="color: {THEME_PRIMARY_COLOR};">📖 Documentation</h4>
                <p style="color: {THEME_TEXT_COLOR}; font-size: 0.9rem;">Learn how to use all features</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="card" style="text-align: center; background: {THEME_CARD_COLOR};">
                <h4 style="color: {THEME_PRIMARY_COLOR};">🐛 Report Issues</h4>
                <p style="color: {THEME_TEXT_COLOR}; font-size: 0.9rem;">Help us improve the app</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown(f"""
            <div class="card" style="text-align: center; background: {THEME_CARD_COLOR};">
                <h4 style="color: {THEME_PRIMARY_COLOR};">💡 Feature Requests</h4>
                <p style="color: {THEME_TEXT_COLOR}; font-size: 0.9rem;">Suggest new features</p>
            </div>
            """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
