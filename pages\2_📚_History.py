import streamlit as st
import sqlite3
from datetime import datetime
from database import TutorialDatabase
from config import THEME_PRIMARY_COLOR, THEME_SECONDARY_COLOR, THEME_BACKGROUND_COLOR, THEME_SECONDARY_BACKGROUND_COLOR, THEME_TEXT_COLOR, THEME_CARD_COLOR, THEME_BORDER_COLOR

# Configure the page
st.set_page_config(
    page_title="History - Chatori Padhai",
    page_icon="📚",
    layout="wide"
)

# Load custom CSS theme
def load_css():
    with open("theme.css", "r") as f:
        css = f.read()
    st.markdown(f"<style>{css}</style>", unsafe_allow_html=True)

# Apply custom theme
load_css()

# Navigation header
st.markdown("""
<div style="background: linear-gradient(90deg, #ffa31a 0%, #e5921a 100%); padding: 1rem; margin: -1rem -1rem 2rem -1rem; border-radius: 0 0 15px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h1 style="color: white; margin: 0; font-size: 1.8rem; font-weight: 700;">📚 Chatori Padhai</h1>
        <div style="display: flex; gap: 1rem;">
            <a href="/" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">🏠 Home</a>
            <a href="/Tutorial" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">🎓 Tutorial</a>
            <a href="/History" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.4); border-radius: 8px;">📚 History</a>
            <a href="/Settings" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">⚙️ Settings</a>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

def get_all_conversations():
    """Get all conversations from the database."""
    try:
        db = TutorialDatabase()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.id, c.subject, c.created_at, c.session_id,
                   COUNT(m.id) as message_count,
                   MAX(m.timestamp) as last_activity
            FROM conversations c
            LEFT JOIN messages m ON c.id = m.conversation_id
            GROUP BY c.id, c.subject, c.created_at, c.session_id
            ORDER BY c.created_at DESC
        """)
        
        conversations = []
        for row in cursor.fetchall():
            conversations.append({
                "id": row[0],
                "subject": row[1],
                "created_at": row[2],
                "session_id": row[3],
                "message_count": row[4] or 0,
                "last_activity": row[5] or row[2]
            })
        
        conn.close()
        return conversations
    except Exception as e:
        st.error(f"Error loading conversations: {str(e)}")
        return []

def get_conversation_messages(conversation_id):
    """Get all messages for a specific conversation."""
    try:
        db = TutorialDatabase()
        return db.get_conversation_history(conversation_id)
    except Exception as e:
        st.error(f"Error loading conversation: {str(e)}")
        return []

def delete_conversation(conversation_id):
    """Delete a conversation and all its messages."""
    try:
        db = TutorialDatabase()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        # Delete messages first (foreign key constraint)
        cursor.execute("DELETE FROM messages WHERE conversation_id = ?", (conversation_id,))
        # Delete conversation
        cursor.execute("DELETE FROM conversations WHERE id = ?", (conversation_id,))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error deleting conversation: {str(e)}")
        return False

def main():
    """Main history page."""
    
    # Page header
    st.markdown(f"""
    <div class="card" style="text-align: center; background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR};">
        <h2 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 0.5rem 0;">📚 Learning History</h2>
        <p style="color: {THEME_TEXT_COLOR}; margin: 0;">Review your past tutorial sessions and conversations</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Get all conversations
    conversations = get_all_conversations()
    
    if not conversations:
        st.markdown(f"""
        <div class="card" style="text-align: center; background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
            <h3 style="color: {THEME_SECONDARY_COLOR}; margin: 2rem 0;">📝 No Learning History Yet</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin-bottom: 2rem;">
                Start your first tutorial session to see your learning history here!
            </p>
            <a href="/Tutorial" style="background: {THEME_PRIMARY_COLOR}; color: white; padding: 0.75rem 1.5rem; border-radius: 8px; text-decoration: none; font-weight: 600;">
                🎓 Start Learning
            </a>
        </div>
        """, unsafe_allow_html=True)
        return
    
    # Search and filter options
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 Search conversations", placeholder="Search by subject...")
    
    with col2:
        sort_option = st.selectbox("📊 Sort by", ["Newest First", "Oldest First", "Most Messages", "Subject A-Z"])
    
    with col3:
        show_details = st.checkbox("📋 Show Details", value=False)
    
    # Filter conversations based on search
    filtered_conversations = conversations
    if search_term:
        filtered_conversations = [
            conv for conv in conversations 
            if search_term.lower() in conv["subject"].lower()
        ]
    
    # Sort conversations
    if sort_option == "Oldest First":
        filtered_conversations.sort(key=lambda x: x["created_at"])
    elif sort_option == "Most Messages":
        filtered_conversations.sort(key=lambda x: x["message_count"], reverse=True)
    elif sort_option == "Subject A-Z":
        filtered_conversations.sort(key=lambda x: x["subject"].lower())
    # Default is "Newest First" which is already sorted
    
    # Display statistics
    total_conversations = len(conversations)
    total_messages = sum(conv["message_count"] for conv in conversations)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown(f"""
        <div class="card" style="text-align: center; background: {THEME_CARD_COLOR}; border-left: 4px solid {THEME_PRIMARY_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0;">{total_conversations}</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0; font-size: 0.9rem;">Total Sessions</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="card" style="text-align: center; background: {THEME_CARD_COLOR}; border-left: 4px solid {THEME_PRIMARY_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0;">{total_messages}</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0; font-size: 0.9rem;">Total Messages</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        avg_messages = round(total_messages / total_conversations, 1) if total_conversations > 0 else 0
        st.markdown(f"""
        <div class="card" style="text-align: center; background: {THEME_CARD_COLOR}; border-left: 4px solid {THEME_PRIMARY_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0;">{avg_messages}</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0; font-size: 0.9rem;">Avg Messages</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        subjects_count = len(set(conv["subject"] for conv in conversations))
        st.markdown(f"""
        <div class="card" style="text-align: center; background: {THEME_CARD_COLOR}; border-left: 4px solid {THEME_PRIMARY_COLOR};">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0;">{subjects_count}</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0; font-size: 0.9rem;">Unique Subjects</p>
        </div>
        """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # Display conversations
    if not filtered_conversations:
        st.markdown(f"""
        <div class="card" style="text-align: center; background: {THEME_CARD_COLOR};">
            <h3 style="color: {THEME_SECONDARY_COLOR};">🔍 No conversations found</h3>
            <p style="color: {THEME_TEXT_COLOR};">Try adjusting your search terms.</p>
        </div>
        """, unsafe_allow_html=True)
        return
    
    # Conversation list
    for i, conv in enumerate(filtered_conversations):
        created_date = datetime.fromisoformat(conv["created_at"].replace('Z', '+00:00')).strftime("%B %d, %Y at %I:%M %p")
        
        # Create expandable conversation card
        with st.expander(f"📖 {conv['subject']} - {created_date}", expanded=False):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"""
                **Subject:** {conv['subject']}  
                **Created:** {created_date}  
                **Messages:** {conv['message_count']}  
                **Session ID:** `{conv['session_id'][:8]}...`
                """)
                
                if show_details:
                    # Load and display conversation messages
                    messages = get_conversation_messages(conv["id"])
                    
                    if messages:
                        st.markdown("**Conversation Preview:**")
                        for j, msg in enumerate(messages[:6]):  # Show first 6 messages
                            role_icon = "👤" if msg["role"] == "user" else "🤖"
                            content_preview = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                            st.markdown(f"**{role_icon} {msg['role'].title()}:** {content_preview}")
                        
                        if len(messages) > 6:
                            st.markdown(f"*... and {len(messages) - 6} more messages*")
            
            with col2:
                # Action buttons
                col_a, col_b = st.columns(2)
                
                with col_a:
                    if st.button("🔄 Resume", key=f"resume_{conv['id']}", use_container_width=True):
                        # Set session state to resume this conversation
                        st.session_state.current_conversation_id = conv["id"]
                        st.session_state.subject = conv["subject"]
                        
                        # Load conversation history
                        messages = get_conversation_messages(conv["id"])
                        chat_history = []
                        for msg in messages:
                            chat_history.append({
                                "role": msg["role"],
                                "content": msg["content"],
                                "type": msg.get("message_type", "message")
                            })
                        st.session_state.chat_history = chat_history
                        
                        st.success(f"Resumed conversation: {conv['subject']}")
                        st.markdown("**[Go to Tutorial page to continue →](/Tutorial)**")
                
                with col_b:
                    if st.button("🗑️ Delete", key=f"delete_{conv['id']}", use_container_width=True):
                        if delete_conversation(conv["id"]):
                            st.success("Conversation deleted!")
                            st.rerun()

if __name__ == "__main__":
    main()
