/* <PERSON><PERSON><PERSON> - Modern Orange/Black Professional Theme */

/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

/* Root variables for consistent theming - Using exact specified colors */
:root {
    --primary-orange: #ffa31a;      /* Primary Orange (255,163,26) */
    --secondary-gray: #808080;      /* Secondary Gray (128,128,128) */
    --background-black: #1b1b1b;    /* Background Black (27,27,27) */
    --dark-gray: #292929;           /* Dark Gray (41,41,41) */
    --text-white: #ffffff;          /* Text White (255,255,255) */
    --hover-orange: #e5921a;        /* Darker orange for hover effects */
    --light-orange: #ffb84d;        /* Lighter orange for gradients */
    --shadow-color: rgba(255, 163, 26, 0.2);  /* Orange shadow */
    --shadow-dark: rgba(0, 0, 0, 0.4);        /* Dark shadow */
}

/* Main app background */
.stApp {
    background-color: var(--background-black) !important;
    font-family: "Inter", sans-serif !important;
    color: var(--text-white) !important;
}

/* Main container */
.main .block-container {
    background-color: var(--background-black) !important;
    padding-top: 1rem !important;
    max-width: 1200px !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* Sidebar styling */
.css-1d391kg, .css-1lcbmhc, .css-17eq0hr {
    background-color: var(--dark-gray) !important;
    color: var(--text-white) !important;
}

/* Hide sidebar by default for cleaner look */
.css-1rs6os {
    background-color: var(--dark-gray) !important;
}

/* Headers */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-white) !important;
    font-family: "Inter", sans-serif !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
}

h1 {
    color: var(--primary-orange) !important;
    text-align: center !important;
    margin-bottom: 2rem !important;
    font-size: 2.5rem !important;
    font-weight: 800 !important;
}

/* Text elements */
p, div, span, label, .stMarkdown {
    color: var(--text-white) !important;
    font-family: "Inter", sans-serif !important;
}

/* Links */
a {
    color: var(--primary-orange) !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

a:hover {
    color: var(--light-orange) !important;
    transform: translateY(-1px) !important;
}

/* Chat messages container */
.stChatMessage {
    background-color: var(--card-black) !important;
    border: 1px solid var(--border-gray) !important;
    border-radius: 10px !important;
    margin: 0.5rem 0 !important;
    padding: 1rem !important;
}

/* User messages */
.stChatMessage[data-testid="chat-message-user"] {
    background-color: var(--secondary-black) !important;
    border-left: 4px solid var(--primary-orange) !important;
}

/* Assistant messages */
.stChatMessage[data-testid="chat-message-assistant"] {
    background-color: var(--card-black) !important;
    border-left: 4px solid var(--light-orange) !important;
}

/* Chat input */
.stChatInput > div > div > div {
    background-color: var(--secondary-black) !important;
    border: 2px solid var(--border-gray) !important;
    border-radius: 25px !important;
    color: var(--text-white) !important;
}

.stChatInput > div > div > div:focus {
    border-color: var(--primary-orange) !important;
    box-shadow: 0 0 0 2px rgba(255, 163, 26, 0.2) !important;
}

/* Buttons */
.stButton > button {
    background: linear-gradient(135deg, var(--primary-orange), var(--hover-orange)) !important;
    color: white !important;
    border: none !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    font-family: "Inter", sans-serif !important;
    padding: 0.75rem 1.5rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 6px var(--shadow-dark) !important;
    font-size: 0.95rem !important;
    letter-spacing: 0.025em !important;
}

.stButton > button:hover {
    background: linear-gradient(135deg, var(--hover-orange), var(--primary-orange)) !important;
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: 0 8px 15px var(--shadow-color) !important;
}

.stButton > button:active {
    transform: translateY(-1px) scale(1.01) !important;
    box-shadow: 0 4px 8px var(--shadow-color) !important;
}

/* Primary buttons */
.stButton > button[kind="primary"] {
    background: linear-gradient(135deg, var(--primary-orange), var(--light-orange)) !important;
    box-shadow: 0 6px 12px var(--shadow-color) !important;
    font-weight: 700 !important;
}

.stButton > button[kind="primary"]:hover {
    background: linear-gradient(135deg, var(--light-orange), var(--primary-orange)) !important;
    box-shadow: 0 10px 20px var(--shadow-color) !important;
    transform: translateY(-3px) scale(1.03) !important;
}

/* Secondary buttons */
.stButton > button[kind="secondary"] {
    background: transparent !important;
    border: 2px solid var(--secondary-gray) !important;
    color: var(--text-white) !important;
}

.stButton > button[kind="secondary"]:hover {
    border-color: var(--primary-orange) !important;
    background: rgba(255, 163, 26, 0.1) !important;
    color: var(--primary-orange) !important;
}

/* Text input fields */
.stTextInput > div > div > input {
    background: linear-gradient(135deg, var(--dark-gray), rgba(41, 41, 41, 0.8)) !important;
    border: 2px solid var(--secondary-gray) !important;
    border-radius: 12px !important;
    color: var(--text-white) !important;
    font-family: "Inter", sans-serif !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.stTextInput > div > div > input:focus {
    border-color: var(--primary-orange) !important;
    box-shadow: 0 0 0 3px var(--shadow-color) !important;
    background: linear-gradient(135deg, var(--dark-gray), rgba(255, 163, 26, 0.05)) !important;
    transform: scale(1.02) !important;
}

.stTextInput > div > div > input::placeholder {
    color: var(--secondary-gray) !important;
    opacity: 0.8 !important;
}

/* Select boxes */
.stSelectbox > div > div > div {
    background-color: var(--secondary-black) !important;
    border: 2px solid var(--border-gray) !important;
    border-radius: 8px !important;
    color: var(--text-white) !important;
}

/* Text areas */
.stTextArea > div > div > textarea {
    background-color: var(--secondary-black) !important;
    border: 2px solid var(--border-gray) !important;
    border-radius: 8px !important;
    color: var(--text-white) !important;
    font-family: "Inter", sans-serif !important;
}

.stTextArea > div > div > textarea:focus {
    border-color: var(--primary-orange) !important;
    box-shadow: 0 0 0 2px rgba(255, 163, 26, 0.2) !important;
}

/* Expander */
.streamlit-expanderHeader {
    background-color: var(--secondary-black) !important;
    color: var(--text-white) !important;
    border: 1px solid var(--border-gray) !important;
    border-radius: 8px !important;
}

.streamlit-expanderContent {
    background-color: var(--card-black) !important;
    border: 1px solid var(--border-gray) !important;
    border-top: none !important;
    border-radius: 0 0 8px 8px !important;
}

/* Tabs */
.stTabs [data-baseweb="tab-list"] {
    background-color: var(--secondary-black) !important;
    border-radius: 8px !important;
}

.stTabs [data-baseweb="tab"] {
    background-color: transparent !important;
    color: var(--text-gray) !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
}

.stTabs [aria-selected="true"] {
    background-color: var(--primary-orange) !important;
    color: white !important;
}

/* Metrics */
.metric-container {
    background-color: var(--card-black) !important;
    border: 1px solid var(--border-gray) !important;
    border-radius: 12px !important;
    padding: 1rem !important;
    margin: 0.5rem 0 !important;
}

/* Success/Info/Warning/Error messages */
.stSuccess {
    background-color: var(--card-black) !important;
    border-left: 4px solid #00ff88 !important;
    color: var(--text-white) !important;
}

.stInfo {
    background-color: var(--card-black) !important;
    border-left: 4px solid var(--primary-orange) !important;
    color: var(--text-white) !important;
}

.stWarning {
    background-color: var(--card-black) !important;
    border-left: 4px solid #ffb800 !important;
    color: var(--text-white) !important;
}

.stError {
    background-color: var(--card-black) !important;
    border-left: 4px solid #ff4444 !important;
    color: var(--text-white) !important;
}

/* Progress bar */
.stProgress > div > div > div {
    background-color: var(--primary-orange) !important;
}

/* Spinner */
.stSpinner > div {
    border-top-color: var(--primary-orange) !important;
}

/* Code blocks */
pre {
    background-color: var(--secondary-black) !important;
    border: 1px solid var(--border-gray) !important;
    border-radius: 8px !important;
    color: var(--text-white) !important;
}

code {
    background-color: var(--secondary-black) !important;
    color: var(--primary-orange) !important;
    padding: 0.2rem 0.4rem !important;
    border-radius: 4px !important;
}

/* Dataframes */
.stDataFrame {
    background-color: var(--card-black) !important;
    border: 1px solid var(--border-gray) !important;
    border-radius: 8px !important;
}

/* Plotly charts background */
.js-plotly-plot .plotly .main-svg {
    background-color: var(--card-black) !important;
}

/* Sidebar widgets */
.stSidebar .stSelectbox > div > div > div,
.stSidebar .stTextInput > div > div > input,
.stSidebar .stTextArea > div > div > textarea {
    background-color: var(--background-black) !important;
    border: 2px solid var(--border-gray) !important;
    color: var(--text-white) !important;
}

/* Custom utility classes */
.orange-text {
    color: var(--primary-orange) !important;
}

.gradient-text {
    background: linear-gradient(
        45deg,
        var(--primary-orange),
        var(--light-orange)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.card {
    background: linear-gradient(135deg, var(--dark-gray), rgba(41, 41, 41, 0.8)) !important;
    border: 1px solid var(--secondary-gray) !important;
    border-radius: 15px !important;
    padding: 1.5rem !important;
    margin: 1rem 0 !important;
    box-shadow: 0 8px 16px var(--shadow-dark) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    backdrop-filter: blur(10px) !important;
}

.card:hover {
    border-color: var(--primary-orange) !important;
    box-shadow: 0 12px 24px var(--shadow-color) !important;
    transform: translateY(-4px) scale(1.01) !important;
    background: linear-gradient(135deg, var(--dark-gray), rgba(255, 163, 26, 0.05)) !important;
}

/* Hide Streamlit branding */
#MainMenu {
    visibility: hidden;
}
footer {
    visibility: hidden;
}
header {
    visibility: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-black);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-orange);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--hover-orange);
}

/* Modern animations and transitions */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Fade-in animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Apply animations */
.main .block-container > div {
    animation: fadeInUp 0.6s ease-out !important;
}

.stButton > button {
    animation: slideInRight 0.4s ease-out !important;
}

/* Hover effects for interactive elements */
.stButton > button:hover,
.card:hover,
a:hover {
    animation: pulse 0.6s ease-in-out !important;
}

/* Loading spinner customization */
.stSpinner > div {
    border-color: var(--primary-orange) transparent var(--primary-orange) transparent !important;
}

/* Progress bar customization */
.stProgress > div > div > div {
    background-color: var(--primary-orange) !important;
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
    .main .block-container {
        padding: 0.5rem !important;
        max-width: 100% !important;
    }

    h1 {
        font-size: 2.5rem !important;
    }

    .stButton > button {
        width: 100% !important;
        margin: 0.5rem 0 !important;
        padding: 1rem !important;
        font-size: 1rem !important;
    }

    .card {
        margin: 0.5rem 0 !important;
        padding: 1rem !important;
    }

    /* Navigation header mobile adjustments */
    .stApp > div:first-child {
        padding: 0.5rem !important;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2rem !important;
    }

    .card {
        padding: 0.75rem !important;
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .stApp {
        background-color: var(--background-black) !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border-width: 2px !important;
    }

    .stButton > button {
        border: 2px solid var(--primary-orange) !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Additional styling for better visual appeal */
.stSelectbox label {
    color: var(--text-white) !important;
    font-weight: 500 !important;
}

.stTextInput label {
    color: var(--text-white) !important;
    font-weight: 500 !important;
}

.stTextArea label {
    color: var(--text-white) !important;
    font-weight: 500 !important;
}

/* Sidebar header styling */
.css-1544g2n {
    color: var(--primary-orange) !important;
    font-weight: 600 !important;
}

/* Sidebar subheader styling */
.css-10trblm {
    color: var(--text-white) !important;
    font-weight: 500 !important;
}

/* Custom markdown styling */
.stMarkdown {
    color: var(--text-white) !important;
}

/* Container styling */
.css-1kyxreq {
    background-color: var(--background-black) !important;
}
