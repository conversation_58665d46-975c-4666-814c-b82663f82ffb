import streamlit as st
import uuid
from datetime import datetime
from tutorial_agent import TutorialAgent
from database import TutorialDatabase
from config import THEME_PRIMARY_COLOR, THEME_SECONDARY_COLOR, THEME_BACKGROUND_COLOR, THEME_SECONDARY_BACKGROUND_COLOR, THEME_TEXT_COLOR, THEME_CARD_COLOR, THEME_BORDER_COLOR

# Configure the Streamlit page
st.set_page_config(
    page_title="Chatori Padhai - AI Tutorial Agent",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Load custom CSS theme
def load_css():
    with open("theme.css", "r") as f:
        css = f.read()
    st.markdown(f"<style>{css}</style>", unsafe_allow_html=True)

# Apply custom theme
load_css()

# Initialize session state
if "session_id" not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())

if "agent" not in st.session_state:
    st.session_state.agent = TutorialAgent()

# Navigation header
st.markdown("""
<div style="background: linear-gradient(90deg, #ffa31a 0%, #e5921a 100%); padding: 1rem; margin: -1rem -1rem 2rem -1rem; border-radius: 0 0 15px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h1 style="color: white; margin: 0; font-size: 1.8rem; font-weight: 700;">🎓 Chatori Padhai</h1>
        <div style="display: flex; gap: 1rem;">
            <a href="/" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.4); border-radius: 8px;">🏠 Home</a>
            <a href="/Tutorial" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">🎓 Tutorial</a>
            <a href="/History" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">📚 History</a>
            <a href="/Settings" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">⚙️ Settings</a>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

def start_new_tutorial(subject_override=None):
    """Start a new tutorial session."""
    # Use override subject if provided, otherwise get from input
    subject = subject_override if subject_override else st.session_state.get("new_subject_input", "")

    if subject and subject.strip():
        try:
            # Start new tutorial
            result = st.session_state.agent.start_tutorial(
                st.session_state.session_id,
                subject.strip()
            )

            # Update session state
            st.session_state.current_conversation_id = result["conversation_id"]
            st.session_state.subject = subject.strip()
            st.session_state.chat_history = [
                {"role": "assistant", "content": result["response"], "type": "tutorial"}
            ]

            # Clear any stored example subject
            st.session_state.selected_example_subject = ""

            st.success(f"Started tutorial on: {subject}")
            st.rerun()

        except Exception as e:
            st.error(f"Error starting tutorial: {str(e)}")

def send_message(message_override=None):
    """Send a user message and get AI response."""
    # Use override message if provided, otherwise get from input
    user_input = message_override if message_override else st.session_state.get("user_input", "")

    if user_input and user_input.strip() and st.session_state.current_conversation_id:
        try:
            # Determine input type
            input_type = "question"
            if "test me" in user_input.lower() or "quiz" in user_input.lower() or "evaluate" in user_input.lower():
                input_type = "evaluation_request"

            # Get AI response
            result = st.session_state.agent.continue_conversation(
                st.session_state.current_conversation_id,
                user_input.strip(),
                input_type
            )

            # Update chat history
            st.session_state.chat_history.append({
                "role": "user",
                "content": user_input.strip(),
                "type": "message"
            })

            st.session_state.chat_history.append({
                "role": "assistant",
                "content": result["response"],
                "type": "response"
            })

            # Clear quick action message if it was used
            if message_override:
                st.session_state.quick_action_message = ""

            st.rerun()

        except Exception as e:
            st.error(f"Error processing message: {str(e)}")

def load_conversation(conversation_id: int):
    """Load a previous conversation."""
    try:
        db = TutorialDatabase()
        history = db.get_conversation_history(conversation_id)

        # Get conversation subject
        import sqlite3
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT subject FROM conversations WHERE id = ?", (conversation_id,))
        result = cursor.fetchone()
        conn.close()

        if result:
            st.session_state.current_conversation_id = conversation_id
            st.session_state.subject = result[0]
            st.session_state.chat_history = []

            # Convert database history to chat format
            for msg in history:
                role = msg["role"]
                content = msg["content"]
                msg_type = msg.get("message_type", "message")

                st.session_state.chat_history.append({
                    "role": role,
                    "content": content,
                    "type": msg_type
                })

            st.success(f"Loaded conversation about: {result[0]}")
            st.rerun()

    except Exception as e:
        st.error(f"Error loading conversation: {str(e)}")

def main():
    """Main home page for Chatori Padhai."""

    # Hero section
    st.markdown(f"""
    <div style="text-align: center; margin: 2rem 0 3rem 0;">
        <h1 style="font-size: 3.5rem; margin: 0; background: linear-gradient(45deg, {THEME_PRIMARY_COLOR}, #e5921a); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 800;">
            🎓 Chatori Padhai
        </h1>
        <p style="font-size: 1.4rem; color: {THEME_SECONDARY_COLOR}; margin: 1rem 0; font-weight: 500;">
            AI-Powered Interactive Learning Platform
        </p>
        <p style="font-size: 1.1rem; color: {THEME_TEXT_COLOR}; margin: 0; max-width: 600px; margin: 0 auto;">
            Master any subject with personalized AI tutoring, interactive Q&A, and knowledge testing
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Quick start section
    st.markdown(f"""
    <div class="card" style="background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR}; text-align: center;">
        <h2 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1.5rem 0;">🚀 Start Learning Now</h2>
        <p style="color: {THEME_TEXT_COLOR}; margin-bottom: 2rem; font-size: 1.1rem;">
            Enter any subject you want to learn about and get started with AI-powered tutoring
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Quick start input
    col1, col2 = st.columns([4, 1])

    with col1:
        quick_subject = st.text_input(
            "Subject to learn",
            placeholder="e.g., Python Programming, Machine Learning, World History, Calculus...",
            key="quick_start_input",
            label_visibility="collapsed"
        )

    with col2:
        if st.button("🎓 Start Learning", type="primary", use_container_width=True):
            if quick_subject and quick_subject.strip():
                start_new_tutorial(quick_subject.strip())
            else:
                st.error("Please enter a subject to learn about!")

    st.markdown("---")

    # Check if user has an active session
    if hasattr(st.session_state, 'current_conversation_id') and st.session_state.current_conversation_id:
        # User has an active session - show quick access
        st.markdown(f"""
        <div class="card" style="background: linear-gradient(135deg, {THEME_PRIMARY_COLOR}, #e5921a); color: white; text-align: center;">
            <h3 style="color: white; margin: 0 0 1rem 0;">🎓 Active Learning Session</h3>
            <p style="color: white; margin: 0 0 1rem 0; font-size: 1.1rem;">
                You're currently learning: <strong>{st.session_state.subject}</strong>
            </p>
            <a href="/Tutorial" style="background: white; color: {THEME_PRIMARY_COLOR}; padding: 0.75rem 1.5rem; border-radius: 8px; text-decoration: none; font-weight: 600;">
                Continue Learning →
            </a>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

    # Features showcase
    st.markdown(f"""
    <div class="card" style="background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
        <h2 style="color: {THEME_PRIMARY_COLOR}; text-align: center; margin-bottom: 2rem;">✨ Why Choose Chatori Padhai?</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.5rem;">
            <div style="padding: 1.5rem; background: linear-gradient(135deg, {THEME_SECONDARY_BACKGROUND_COLOR}, {THEME_CARD_COLOR}); border-radius: 12px; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 4px 6px rgba(0,0,0,0.3);">
                <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    🎓 Interactive Tutorials
                </h3>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; line-height: 1.6;">
                    Get comprehensive, AI-generated tutorials tailored to your learning level and pace
                </p>
            </div>
            <div style="padding: 1.5rem; background: linear-gradient(135deg, {THEME_SECONDARY_BACKGROUND_COLOR}, {THEME_CARD_COLOR}); border-radius: 12px; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 4px 6px rgba(0,0,0,0.3);">
                <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    💬 Smart Q&A
                </h3>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; line-height: 1.6;">
                    Ask follow-up questions and get detailed, contextual explanations from your AI tutor
                </p>
            </div>
            <div style="padding: 1.5rem; background: linear-gradient(135deg, {THEME_SECONDARY_BACKGROUND_COLOR}, {THEME_CARD_COLOR}); border-radius: 12px; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 4px 6px rgba(0,0,0,0.3);">
                <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    🧠 Knowledge Testing
                </h3>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; line-height: 1.6;">
                    Test your understanding with AI-generated quizzes and get instant feedback
                </p>
            </div>
            <div style="padding: 1.5rem; background: linear-gradient(135deg, {THEME_SECONDARY_BACKGROUND_COLOR}, {THEME_CARD_COLOR}); border-radius: 12px; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 4px 6px rgba(0,0,0,0.3);">
                <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    📚 Learning History
                </h3>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; line-height: 1.6;">
                    Track your progress and revisit previous learning sessions anytime
                </p>
            </div>
            <div style="padding: 1.5rem; background: linear-gradient(135deg, {THEME_SECONDARY_BACKGROUND_COLOR}, {THEME_CARD_COLOR}); border-radius: 12px; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 4px 6px rgba(0,0,0,0.3);">
                <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    🎨 Modern Interface
                </h3>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; line-height: 1.6;">
                    Enjoy a sleek, professional interface designed for optimal learning experience
                </p>
            </div>
            <div style="padding: 1.5rem; background: linear-gradient(135deg, {THEME_SECONDARY_BACKGROUND_COLOR}, {THEME_CARD_COLOR}); border-radius: 12px; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 4px 6px rgba(0,0,0,0.3);">
                <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    🔒 Privacy First
                </h3>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; line-height: 1.6;">
                    Your learning data stays private and secure with local storage
                </p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    st.markdown("---")

    # Popular topics section
    st.markdown(f"""
    <div class="card" style="background: {THEME_CARD_COLOR}; border: 1px solid {THEME_BORDER_COLOR};">
        <h2 style="color: {THEME_PRIMARY_COLOR}; text-align: center; margin-bottom: 1rem;">🔥 Popular Learning Topics</h2>
        <p style="text-align: center; color: {THEME_SECONDARY_COLOR}; margin-bottom: 2rem;">
            Click any topic below to start learning immediately
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Popular subjects grid
    example_subjects = [
        "Python Programming", "Machine Learning", "Data Science",
        "Web Development", "Statistics", "Linear Algebra",
        "Computer Networks", "Database Design", "API Development",
        "React.js", "Docker", "Git Version Control"
    ]

    cols = st.columns(3)
    for i, subject in enumerate(example_subjects):
        col_idx = i % 3
        with cols[col_idx]:
            if st.button(f"📚 {subject}", key=f"popular_{i}", use_container_width=True):
                start_new_tutorial(subject)

    st.markdown("---")

    # Navigation cards
    st.markdown(f"""
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
        <div class="card" style="text-align: center; background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR}; transition: transform 0.3s ease;">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0;">🎓 Start Tutorial</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0 0 1.5rem 0;">Begin learning any subject with AI-powered tutorials</p>
            <a href="/Tutorial" style="background: {THEME_PRIMARY_COLOR}; color: white; padding: 0.75rem 1.5rem; border-radius: 8px; text-decoration: none; font-weight: 600;">Go to Tutorial</a>
        </div>
        <div class="card" style="text-align: center; background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR}; transition: transform 0.3s ease;">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0;">📚 View History</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0 0 1.5rem 0;">Review and continue your previous learning sessions</p>
            <a href="/History" style="background: {THEME_PRIMARY_COLOR}; color: white; padding: 0.75rem 1.5rem; border-radius: 8px; text-decoration: none; font-weight: 600;">View History</a>
        </div>
        <div class="card" style="text-align: center; background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR}; transition: transform 0.3s ease;">
            <h3 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 1rem 0;">⚙️ Settings</h3>
            <p style="color: {THEME_TEXT_COLOR}; margin: 0 0 1.5rem 0;">Customize your learning experience and preferences</p>
            <a href="/Settings" style="background: {THEME_PRIMARY_COLOR}; color: white; padding: 0.75rem 1.5rem; border-radius: 8px; text-decoration: none; font-weight: 600;">Open Settings</a>
        </div>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
