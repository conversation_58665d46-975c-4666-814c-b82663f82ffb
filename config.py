"""
Configuration settings for the AI Tutorial Agent.
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database Configuration
DATABASE_PATH = os.getenv("DATABASE_PATH", "tutorial_agent.db")

# LLM Configuration
LLM_MODEL = os.getenv("LLM_MODEL", "meta-llama/llama-4-scout")
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.7"))
MAX_CONTEXT_MESSAGES = 5  # Number of previous messages to include for context

# Site Configuration
SITE_URL = os.getenv("SITE_URL", "http://localhost:8501")
SITE_NAME = os.getenv("SITE_NAME", "Chatori Padhai")

# Theme Configuration - Orange/Black Professional Theme
# Using exact colors specified: Primary Orange: #ffa31a (255,163,26), Secondary Gray: #808080 (128,128,128),
# Dark Gray: #292929 (41,41,41), Background Black: #1b1b1b (27,27,27), Text White: #ffffff (255,255,255)
THEME_PRIMARY_COLOR = os.getenv("THEME_PRIMARY_COLOR", "#ffa31a")  # Primary Orange (255,163,26)
THEME_SECONDARY_COLOR = os.getenv("THEME_SECONDARY_COLOR", "#808080")  # Secondary Gray (128,128,128)
THEME_BACKGROUND_COLOR = os.getenv("THEME_BACKGROUND_COLOR", "#1b1b1b")  # Background Black (27,27,27)
THEME_SECONDARY_BACKGROUND_COLOR = os.getenv("THEME_SECONDARY_BACKGROUND_COLOR", "#292929")  # Dark Gray (41,41,41)
THEME_TEXT_COLOR = os.getenv("THEME_TEXT_COLOR", "#ffffff")  # Text White (255,255,255)
THEME_CARD_COLOR = os.getenv("THEME_CARD_COLOR", "#292929")  # Dark Gray for cards (41,41,41)
THEME_BORDER_COLOR = os.getenv("THEME_BORDER_COLOR", "#808080")  # Secondary Gray for borders (128,128,128)

# Tutorial Generation Settings
TUTORIAL_LENGTH_TARGET = "300-500 words"
TUTORIAL_DIFFICULTY_LEVEL = "beginners to intermediate learners"

# Evaluation Settings
EVALUATION_QUESTION_FORMAT = "1-3 sentences"
MAX_EVALUATIONS_PER_SESSION = 10

# UI Configuration
STREAMLIT_PAGE_TITLE = "AI Tutorial Agent"
STREAMLIT_PAGE_ICON = "🤖"
STREAMLIT_LAYOUT = "wide"

# Response Templates
TUTORIAL_PROMPT_TEMPLATE = """You are an expert AI tutor. Create a comprehensive but concise tutorial about {subject}.

Structure your response as follows:
1. Brief introduction to the topic
2. Key concepts and definitions
3. Important examples
4. Common applications or use cases
5. Tips for further learning

Keep the tutorial engaging, educational, and appropriate for {difficulty_level}.
Use clear examples and explanations. Aim for about {length_target}."""

QUESTION_PROMPT_TEMPLATE = """You are an expert AI tutor teaching about {subject}.

Previous conversation context:
{context}

The student has asked: "{question}"

Provide a clear, detailed explanation that directly answers their question. Use examples where helpful.
Be encouraging and educational. If the question is off-topic, gently guide them back to {subject}."""

EVALUATION_PROMPT_TEMPLATE = """You are an expert AI tutor. Based on the tutorial content about {subject}, create a thoughtful evaluation question.

Tutorial content covered:
{content}

Create ONE evaluation question that:
1. Tests understanding of key concepts
2. Is neither too easy nor too difficult
3. Requires the student to demonstrate comprehension
4. Can be answered in {answer_format}

Format your response as:
QUESTION: [Your question here]

This is evaluation question #{question_number}."""

FEEDBACK_PROMPT_TEMPLATE = """You are an expert AI tutor evaluating a student's answer about {subject}.

Evaluation Question: {question}
Student's Answer: {answer}

Provide constructive feedback that:
1. Acknowledges what the student got right
2. Gently corrects any misconceptions
3. Provides additional clarification if needed
4. Encourages continued learning

Be supportive and educational. Rate their understanding and provide specific feedback."""

# Error Messages
ERROR_MESSAGES = {
    "api_error": "I apologize, but I encountered an error connecting to the AI service. Please try again.",
    "database_error": "There was an issue saving your conversation. Please try again.",
    "conversation_not_found": "I couldn't find that conversation. Please start a new tutorial or check the conversation ID.",
    "no_subject": "Please specify a subject you'd like to learn about.",
    "general_error": "Something went wrong. Please try again or contact support if the issue persists."
}

# Success Messages
SUCCESS_MESSAGES = {
    "tutorial_started": "Great! I've prepared a tutorial on {subject}. Let's start learning!",
    "conversation_loaded": "Welcome back! I've loaded your previous conversation about {subject}.",
    "evaluation_complete": "Well done! You're making good progress in understanding {subject}."
}

# Quick Action Buttons
QUICK_ACTIONS = [
    {
        "label": "📝 More Examples",
        "prompt": "Can you provide more examples?"
    },
    {
        "label": "🔍 Explain Further",
        "prompt": "Can you explain this topic in more detail?"
    },
    {
        "label": "🎯 Real Applications",
        "prompt": "What are some real-world applications of this?"
    },
    {
        "label": "📚 Next Steps",
        "prompt": "What should I learn next?"
    }
]

# Example Subjects for the welcome screen
EXAMPLE_SUBJECTS = [
    "Python Programming",
    "Machine Learning",
    "Data Science",
    "Web Development",
    "Statistics",
    "Linear Algebra",
    "Computer Networks",
    "Database Design",
    "API Development",
    "React.js",
    "Docker",
    "Git Version Control"
]

# Logging Configuration
ENABLE_LOGGING = True
LOG_LEVEL = "INFO"
LOG_FILE = "tutorial_agent.log"
