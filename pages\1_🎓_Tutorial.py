import streamlit as st
import uuid
from datetime import datetime
from tutorial_agent import Tutorial<PERSON>gent
from database import TutorialDatabase
from config import THEME_PRIMARY_COLOR, THEME_SECONDARY_COLOR, THEME_BACKGROUND_COLOR, THEME_SECONDARY_BACKGROUND_COLOR, THEME_TEXT_COLOR, THEME_CARD_COLOR, THEME_BORDER_COLOR

# Configure the page
st.set_page_config(
    page_title="Tutorial - Chatori Padhai",
    page_icon="🎓",
    layout="wide"
)

# Load custom CSS theme
def load_css():
    with open("theme.css", "r") as f:
        css = f.read()
    st.markdown(f"<style>{css}</style>", unsafe_allow_html=True)

# Apply custom theme
load_css()

# Initialize session state
if "session_id" not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())

if "agent" not in st.session_state:
    st.session_state.agent = TutorialAgent()

if "current_conversation_id" not in st.session_state:
    st.session_state.current_conversation_id = None

if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

if "subject" not in st.session_state:
    st.session_state.subject = ""

if "selected_example_subject" not in st.session_state:
    st.session_state.selected_example_subject = ""

if "quick_action_message" not in st.session_state:
    st.session_state.quick_action_message = ""

# Navigation header
st.markdown("""
<div style="background: linear-gradient(90deg, #ffa31a 0%, #e5921a 100%); padding: 1rem; margin: -1rem -1rem 2rem -1rem; border-radius: 0 0 15px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h1 style="color: white; margin: 0; font-size: 1.8rem; font-weight: 700;">🎓 Chatori Padhai</h1>
        <div style="display: flex; gap: 1rem;">
            <a href="/" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">🏠 Home</a>
            <a href="/Tutorial" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.4); border-radius: 8px;">🎓 Tutorial</a>
            <a href="/History" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">📚 History</a>
            <a href="/Settings" style="color: white; text-decoration: none; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 8px; transition: all 0.3s;">⚙️ Settings</a>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

def start_new_tutorial(subject=None):
    """Start a new tutorial session."""
    if subject is None:
        subject = st.session_state.get("new_subject_input", "").strip()
    
    if not subject:
        st.error("Please enter a subject to learn about!")
        return
    
    try:
        # Start new tutorial
        result = st.session_state.agent.start_tutorial(
            st.session_state.session_id,
            subject
        )
        
        # Update session state
        st.session_state.current_conversation_id = result["conversation_id"]
        st.session_state.subject = subject
        st.session_state.chat_history = [{
            "role": "assistant",
            "content": result["response"],
            "type": "tutorial"
        }]
        
        # Clear inputs
        st.session_state.selected_example_subject = ""
        st.session_state.new_subject_input = ""
        
        st.success(f"Started learning about: {subject}")
        st.rerun()
        
    except Exception as e:
        st.error(f"Error starting tutorial: {str(e)}")

def send_message(message=None):
    """Send a message in the current conversation."""
    if message is None:
        message = st.session_state.get("user_input", "").strip()
    
    if not message:
        return
    
    if not st.session_state.current_conversation_id:
        st.error("Please start a tutorial first!")
        return
    
    try:
        # Determine input type
        input_type = "question"
        if "test me" in message.lower() or "quiz" in message.lower() or "evaluate" in message.lower():
            input_type = "evaluation_request"
        
        # Get AI response
        result = st.session_state.agent.continue_conversation(
            st.session_state.current_conversation_id,
            message,
            input_type
        )
        
        # Update chat history
        st.session_state.chat_history.append({
            "role": "user",
            "content": message,
            "type": "message"
        })
        
        st.session_state.chat_history.append({
            "role": "assistant",
            "content": result["response"],
            "type": "response"
        })
        
        # Clear inputs
        st.session_state.user_input = ""
        st.session_state.quick_action_message = ""
        
        st.rerun()
        
    except Exception as e:
        st.error(f"Error sending message: {str(e)}")

def main():
    """Main tutorial page."""
    
    # Check if an example subject was selected
    if st.session_state.selected_example_subject:
        start_new_tutorial(st.session_state.selected_example_subject)
        return
    
    # Check if a quick action was triggered
    if st.session_state.quick_action_message:
        send_message(st.session_state.quick_action_message)
        return
    
    # Sidebar for tutorial management
    with st.sidebar:
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, {THEME_PRIMARY_COLOR}, #e5921a); border-radius: 10px; margin-bottom: 1rem;">
            <h3 style="color: white; margin: 0;">📚 Tutorial Control</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # New tutorial section
        st.subheader("Start New Tutorial")
        new_subject = st.text_input(
            "What would you like to learn about?",
            placeholder="e.g., Python functions, Machine Learning, History of Rome...",
            key="new_subject_input"
        )
        
        if st.button("🚀 Start Tutorial", type="primary", use_container_width=True):
            start_new_tutorial()
        
        st.markdown("---")
        
        # Current session info
        if st.session_state.current_conversation_id:
            st.markdown(f"""
            <div class="card" style="background: {THEME_CARD_COLOR}; padding: 1rem; border-radius: 8px;">
                <h4 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 0.5rem 0;">📖 Current Session</h4>
                <p style="color: {THEME_TEXT_COLOR}; margin: 0; font-weight: 500;">{st.session_state.subject}</p>
                <p style="color: {THEME_SECONDARY_COLOR}; margin: 0.5rem 0 0 0; font-size: 0.9rem;">
                    Messages: {len(st.session_state.chat_history)}
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            if st.button("🔄 New Session", use_container_width=True):
                st.session_state.current_conversation_id = None
                st.session_state.chat_history = []
                st.session_state.subject = ""
                st.rerun()
        
        st.markdown("---")
        
        # Quick help
        st.subheader("💡 How to Use")
        st.markdown("""
        1. **Start**: Enter a subject above
        2. **Learn**: Read the AI tutorial
        3. **Ask**: Ask follow-up questions
        4. **Test**: Say "test me" for quizzes
        5. **Navigate**: Use the top menu
        """)
    
    # Main content area
    if st.session_state.current_conversation_id:
        # Current tutorial header
        st.markdown(f"""
        <div class="card" style="background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR};">
            <h2 style="color: {THEME_PRIMARY_COLOR}; margin: 0 0 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                📖 Learning: {st.session_state.subject}
            </h2>
            <p style="color: {THEME_SECONDARY_COLOR}; margin: 0;">Interactive AI Tutorial Session</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Chat messages
        chat_container = st.container()
        with chat_container:
            for i, message in enumerate(st.session_state.chat_history):
                if message["role"] == "user":
                    st.markdown(f"""
                    <div style="display: flex; justify-content: flex-end; margin: 1rem 0;">
                        <div style="background: linear-gradient(135deg, {THEME_PRIMARY_COLOR}, #e5921a); color: white; padding: 1rem; border-radius: 15px 15px 5px 15px; max-width: 70%; box-shadow: 0 2px 10px rgba(255,163,26,0.3);">
                            <strong>You:</strong><br>{message['content']}
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div style="display: flex; justify-content: flex-start; margin: 1rem 0;">
                        <div style="background: {THEME_CARD_COLOR}; color: {THEME_TEXT_COLOR}; padding: 1rem; border-radius: 15px 15px 15px 5px; max-width: 80%; border-left: 4px solid {THEME_PRIMARY_COLOR}; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                            <strong style="color: {THEME_PRIMARY_COLOR};">🤖 Chatori Padhai:</strong><br>{message['content']}
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
        
        # Input area
        st.markdown("---")
        col1, col2, col3 = st.columns([6, 1, 1])
        
        with col1:
            user_input = st.text_input(
                "Ask a question or request evaluation:",
                placeholder="e.g., Can you explain this in more detail? or Test my understanding!",
                key="user_input"
            )
        
        with col2:
            if st.button("💬 Send", use_container_width=True):
                send_message()
        
        with col3:
            if st.button("🧠 Test Me", use_container_width=True):
                if st.session_state.current_conversation_id:
                    st.session_state.quick_action_message = "Please test my understanding with a question."
                    st.rerun()
        
        # Quick action buttons
        st.markdown("### ⚡ Quick Actions")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("📝 More Examples", use_container_width=True):
                st.session_state.quick_action_message = "Can you provide more examples?"
                st.rerun()
        
        with col2:
            if st.button("🔍 Explain Further", use_container_width=True):
                st.session_state.quick_action_message = "Can you explain this in more detail?"
                st.rerun()
        
        with col3:
            if st.button("💡 Practical Uses", use_container_width=True):
                st.session_state.quick_action_message = "What are some practical applications?"
                st.rerun()
        
        with col4:
            if st.button("📊 Summary", use_container_width=True):
                st.session_state.quick_action_message = "Can you provide a summary of what we've covered?"
                st.rerun()
    
    else:
        # No active tutorial - show start options
        st.markdown(f"""
        <div class="card" style="text-align: center; background: linear-gradient(135deg, {THEME_CARD_COLOR}, {THEME_SECONDARY_BACKGROUND_COLOR}); border: 2px solid {THEME_PRIMARY_COLOR};">
            <h2 style="color: {THEME_PRIMARY_COLOR}; margin-bottom: 1rem;">🎓 Ready to Learn?</h2>
            <p style="color: {THEME_TEXT_COLOR}; font-size: 1.1rem; margin-bottom: 1.5rem;">
                Start a new tutorial session by entering a subject in the sidebar, or choose from popular topics below.
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Popular subjects
        st.markdown("### 🔥 Popular Learning Topics")
        example_subjects = [
            "Python Programming", "Machine Learning", "Data Science",
            "Web Development", "Statistics", "Linear Algebra",
            "Computer Networks", "Database Design", "API Development",
            "React.js", "Docker", "Git Version Control"
        ]
        
        cols = st.columns(3)
        for i, subject in enumerate(example_subjects):
            col_idx = i % 3
            with cols[col_idx]:
                if st.button(f"📚 {subject}", key=f"example_{i}", use_container_width=True):
                    st.session_state.selected_example_subject = subject
                    st.rerun()

if __name__ == "__main__":
    main()
